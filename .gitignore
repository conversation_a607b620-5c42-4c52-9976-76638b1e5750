######################################################################
# Build Tools

.gradle
/build/
!gradle/wrapper/gradle-wrapper.jar

HELP.md
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**
!**/src/test/**


######################################################################
# IDE

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### JRebel ###
rebel.xml

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/

### VS Code ###
.vscode/
/elk/log/
*.log
/bases/bases-apps/bases-aliyun/src/main/resources/rebel.xml
/bases/bases-apps/bases-excel/src/main/resources/rebel.xml
/bases/bases-apps/bases-redis/src/main/resources/rebel.xml
/bases/bases-apps/src/main/resources/rebel.xml
/bases/bases-modules/bases-core/src/main/resources/rebel.xml
/bases/bases-modules/bases-sm/src/main/resources/rebel.xml
/bases/bases-modules/bases-sm-entity/src/main/resources/rebel.xml
/bases/bases-modules/bases-sm-security/src/main/resources/rebel.xml
/bases/bases-modules/src/main/resources/rebel.xml
/bases/bases-test/src/main/resources/rebel.xml
/linkapp-api/src/main/resources/rebel.xml
*.xml.versionsBackup
*.swp

!*/build/*.java
!*/build/*.html
!*/build/*.xml

/sql/linkappdb.zip

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
temp/

# Documentation (not tracked)
docs/



