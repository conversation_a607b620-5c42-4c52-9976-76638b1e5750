<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.easylinkin.linkappapi.followshift.mapper.RailUserFollowProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.easylinkin.linkappapi.followshift.entity.RailUserFollowProject">
        <id column="id_" property="id"/>
        <result column="user_id_" property="userId"/>
        <result column="tenant_id_" property="tenantId"/>
        <result column="is_deleted_" property="isDeleted"/>
        <result column="creator_id_" property="creatorId"/>
        <result column="create_time_" property="createTime"/>
        <result column="modify_id_" property="modifyId"/>
        <result column="modify_time_" property="modifyTime"/>
        <result column="remark_" property="remark"/>
    </resultMap>

    <!-- 根据用户ID查询关注的项目列表 -->
    <select id="getFollowProjectsByUserId" resultMap="BaseResultMap">
        SELECT t.*
        FROM rail_user_follow_project t
        WHERE t.user_id_ = #{userId}
          AND t.is_deleted_ = 0
        ORDER BY t.create_time_ DESC
    </select>

    <!-- 根据项目ID查询关注该项目的用户列表 -->
    <select id="getFollowUsersByTenantId" resultMap="BaseResultMap">
        SELECT t.*
        FROM rail_user_follow_project t
        WHERE t.tenant_id_ = #{tenantId}
          AND t.is_deleted_ = 0
        ORDER BY t.create_time_ DESC
    </select>

    <!-- 检查用户是否已关注指定项目 -->
    <select id="checkUserFollowProject" resultMap="BaseResultMap">
        SELECT t.*
        FROM rail_user_follow_project t
        WHERE t.user_id_ = #{userId}
          AND t.tenant_id_ = #{tenantId}
          AND t.is_deleted_ = 0
        LIMIT 1
    </select>

</mapper>
