-- =============================================
-- 用户关注项目表
-- 开发者: kyf
-- 日期: 2025-08-05
-- 功能: 用户关注项目管理
-- =============================================

-- 用户关注项目表
CREATE TABLE `rail_user_follow_project` (
    `id_` bigint(20) AUTO_INCREMENT COMMENT 'ID主键',
    `user_id_` bigint(20) DEFAULT NULL COMMENT '用户ID(linkapp_user)',
    `tenant_id_` varchar(50) DEFAULT NULL COMMENT '项目ID(linkapp_tenant)',
    `is_deleted_` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标志',
    `creator_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id_`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户关注项目表';
