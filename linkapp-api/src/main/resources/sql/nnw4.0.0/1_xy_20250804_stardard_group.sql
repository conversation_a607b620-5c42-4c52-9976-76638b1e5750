-- 菜单
-- 1.web菜单
-- 标准化班组 99
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9922', NULL, '标准化班组', 't_standard_group', '标准化班组', 1, NULL, NULL, 9922.000, 1, '', NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);

-- 合格班组验收 992201
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('992201', '9922', '合格班组验收', 't_qualified_group_acceptance', '合格班组验收', 2, NULL, NULL, 9922.100, 1, 'standardization/qualifiedGroupAcceptance', NULL, '1', NULL, NULL, NULL,  NULL, NULL, 0);
-- 合格班组授牌 992202
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('992202', '9922', '合格班组授牌', 't_qualified_group_award', '合格班组授牌', 2, NULL, NULL, 9922.200, 1, 'standardization/qualifiedGroupAward', NULL, '1', NULL, NULL, NULL,  NULL, NULL, 0);
-- 班组自我小立法 992203
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('992203', '9922', '班组自我小立法', 't_group_self_initiative', '班组自我小立法', 2, NULL, NULL, 9922.300, 1, 'standardization/selfLegislation', NULL, '1', NULL, NULL, NULL,  NULL, NULL, 0);
-- 班组三同时 992204
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('992204', '9922', '班组三同时', 't_group_three_together', '班组三同时', 2, NULL, NULL, 9922.400, 1, 'standardization/teamThreeSimultaneous', NULL, '1', NULL, NULL, NULL,  NULL, NULL, 0);
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('9922041', '992204', '编辑', 't_group_three_together:edit', NULL, 3, NULL, NULL, 992204.100, 2, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, 0);
-- 班组日考核 992205
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('992205', '9922', '班组日考核', 't_group_daily_assessment', '班组日考核', 2, NULL, NULL, 9922.500, 1, 'standardization/groupDailyAssessment', NULL, '1', NULL, NULL, NULL,  NULL, NULL, 0);

-- 2.app菜单
-- 班组三同时 661014 appGroupThreeTogether
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('661014', '660000', '班组三同时', 'appGroupThreeTogether', '班组三同时', 1, NULL, NULL, 661014.000, 1, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, 1);
-- 班组日考核 661015 appGroupDailyAssessment
INSERT INTO linkappdb.linkapp_privilege
(id_, parent_id_, name_, privilege_code_, description_, level_, target, search_code_, seq_, type_, url_, is_log_, tenant_id_, create_time_, creator_, modifier_, modify_time_, icon_name, flag_)
VALUES('661015', '660000', '班组日考核', 'appGroupDailyAssessment', '班组日考核', 1, NULL, NULL, 661015.000, 1, NULL, NULL, '1', NULL, NULL, NULL, NULL, NULL, 1);

-- 班组三同时表结构
create table if not exists `rail_group_three_together` (
    `id` bigint(20) not null auto_increment COMMENT '主键ID',
    `type` tinyint(4) not null COMMENT '三同时类型：1-与班组作业人员同吃，2-与班组作业人员同住，3-与班组作业人员同上下班',
    `three_together_time` datetime not null COMMENT '三同时时间',
    `img_list` text COMMENT '图片列表，JSON格式存储图片URL数组',
    `remark` varchar(500) default null COMMENT '备注',
    `uploader_id` bigint(20) not null COMMENT '上传人ID',
    `tenant_id` varchar(50) not null COMMENT '租户ID',
    `delete_state` tinyint(4) not null default '0' COMMENT '删除状态：0-未删除，1-已删除',
    `create_id_` bigint(20) default null COMMENT '创建人id',
    `create_time_` datetime default null COMMENT '创建日期',
    `modify_id_` bigint(20) default null COMMENT '修改人id',
    `modify_time_` datetime default null COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    primary key (`id`),
    key `idx_delete_state` (`delete_state`)
) COMMENT = '班组三同时记录表';