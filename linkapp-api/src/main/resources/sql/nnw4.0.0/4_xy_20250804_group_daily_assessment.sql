-- 班组日考核相关表结构

-- 班组日考核主表
CREATE TABLE IF NOT EXISTS `rail_group_daily_assessment` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `project_name` varchar(200) NOT NULL COMMENT '项目名称',
    `assessment_date` date NOT NULL COMMENT '考核日期',
    `total_score` decimal(5,2) DEFAULT 100.00 COMMENT '应得分',
    `assessment_result` varchar(20) NOT NULL COMMENT '考核结论：优秀、合格、不合格',
    `supervisor_signature` varchar(100) NOT NULL COMMENT '进驻督导人员签字',
    `tenant_id` varchar(50) NOT NULL COMMENT '租户ID',
    `delete_state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除状态：0-未删除，1-已删除',
    `create_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_assessment_date` (`assessment_date`),
    KEY `idx_delete_state` (`delete_state`)
) COMMENT = '班组日考核主表';

-- 班组日考核项表（基于小立法）
CREATE TABLE IF NOT EXISTS `rail_group_daily_assessment_item` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `assessment_id` bigint(20) NOT NULL COMMENT '考核记录ID',
    `item_type` varchar(20) NOT NULL COMMENT '考核项类型：减分项、加分项、否决项',
    `item_content` varchar(500) NOT NULL COMMENT '考核内容',
    `score_unit` varchar(20) NOT NULL COMMENT '计分单位（如：每人次、每次、每台、每件、每班等）',
    `score_value` varchar(100) NOT NULL COMMENT '分数值（如：-2、+2、当天取消考核，判定为不合格等）',
    `deduct_score` decimal(5,2) DEFAULT 0.00 COMMENT '扣分（减分项使用）',
    `bonus_score` decimal(5,2) DEFAULT 0.00 COMMENT '得分（加分项使用）',
    `is_veto` tinyint(1) DEFAULT 0 COMMENT '是否否决（否决项使用）：0-否，1-是',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序序号',
    `tenant_id` varchar(50) NOT NULL COMMENT '租户ID',
    `delete_state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除状态：0-未删除，1-已删除',
    `create_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_assessment_id` (`assessment_id`),
    KEY `idx_item_type` (`item_type`),
    KEY `idx_delete_state` (`delete_state`)
) COMMENT = '班组日考核项表';

-- 班组小立法考核项模板表
CREATE TABLE IF NOT EXISTS `rail_group_legislation_template` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `item_type` varchar(20) NOT NULL COMMENT '考核项类型：减分项、加分项、否决项',
    `item_content` varchar(500) NOT NULL COMMENT '考核内容',
    `score_unit` varchar(20) NOT NULL COMMENT '计分单位（如：每人次、每次、每台、每件、每班等）',
    `score_value` varchar(100) NOT NULL COMMENT '分数值（如：-2、+2、当天取消考核，判定为不合格等）',
    `sort_order` int(11) DEFAULT 0 COMMENT '排序序号',
    `delete_state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除状态：0-未删除，1-已删除',
    `create_id_` bigint(20) DEFAULT NULL COMMENT '创建人id',
    `create_time_` datetime DEFAULT NULL COMMENT '创建日期',
    `modify_id_` bigint(20) DEFAULT NULL COMMENT '修改人id',
    `modify_time_` datetime DEFAULT NULL COMMENT '修改时间',
    `remark_` text COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `idx_item_type` (`item_type`),
    KEY `idx_delete_state` (`delete_state`)
) COMMENT = '班组小立法考核项模板表';

-- 插入小立法模板数据（基于图片中的内容）
INSERT INTO `rail_group_legislation_template` (`item_type`, `item_content`, `score_unit`, `score_value`, `sort_order`, `create_time_`) VALUES
-- 减分项
('减分项', '未正确佩戴劳动防护用品', '每人次', '-2', 1, NOW()),
('减分项', '班组成员未参加班前会', '每人次', '-1', 2, NOW()),
('减分项', '班前会质量较差,被项目公司点名批评', '每次', '-5', 3, NOW()),
('减分项', '机械设备(吊车、挖机)未经许可,擅自使用', '每次', '-20', 4, NOW()),
('减分项', '机械设备使用期间未执行车来机停', '每次', '-5', 5, NOW()),
('减分项', '动火作业未经审批', '每次', '-3', 6, NOW()),
('减分项', '临边护栏防护失效', '每次', '-3', 7, NOW()),
('减分项', '配电箱未上锁或未关闭箱门', '每次', '-2', 8, NOW()),
('减分项', '电气设备和配电箱接地失效,临时用电一闸多机', '每次', '-2', 9, NOW()),
('减分项', '作业范围内安全设施破坏', '每台', '-2', 10, NOW()),
('减分项', '作业范围内有轻质票漂浮物未清理', '每处', '-5', 11, NOW()),
('减分项', '文明施工差', '每件', '-1', 12, NOW()),

-- 加分项
('加分项', '当天接受上级或外部检查,受到认可、得到表扬', '每次', '+2', 13, NOW()),
('加分项', '当天班前会,收到项目公司的表扬', '每次', '+2', 14, NOW()),
('加分项', '安全质量隐患举报', '每条', '+2', 15, NOW()),

-- 否决项
('否决项', '违反安全质量红线底线,未达到安全施工的条件', '每次', '当天取消考核，判定为不合格。', 16, NOW()),
('否决项', '"五步走"要求完成较差,态度不端正,应付了事', '每班', '当天取消考核，判定为不合格。', 17, NOW()),
('否决项', '施工质量不能满足验收标准', '每班', '当天取消考核，判定为不合格。', 18, NOW()),
('否决项', '存在险性事故或事故发生', '每班', '当天取消考核，判定为不合格。', 19, NOW()),
('否决项', '上述减分项大于等于3条', '每班', '当天取消考核，判定为不合格。', 20, NOW());


ALTER TABLE `rail_group_daily_assessment_item`
    MODIFY COLUMN `deduct_score` decimal(5,2) DEFAULT NULL COMMENT '扣分（减分项使用）';

ALTER TABLE `rail_group_daily_assessment_item`
    MODIFY COLUMN `bonus_score` decimal(5,2) DEFAULT NULL COMMENT '得分（加分项使用）';