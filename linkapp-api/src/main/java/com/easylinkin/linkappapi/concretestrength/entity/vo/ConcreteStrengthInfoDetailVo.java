package com.easylinkin.linkappapi.concretestrength.entity.vo;

import com.easylinkin.linkappapi.concretestrength.dto.ConcreteStrengthDetailDTO;
import com.easylinkin.linkappapi.concretestrength.dto.ConcreteStrengthInfoDTO;
import com.easylinkin.linkappapi.concretestrength.entity.ConcreteStrengthInfo;
import com.easylinkin.linkappapi.quality.entity.QualityPosition;
import com.easylinkin.linkappapi.security.entity.LinkappUser;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class ConcreteStrengthInfoDetailVo extends ConcreteStrengthInfo {

    /**
     * 部位信息
     */
    private QualityPosition position;

    /**
     * 检测人
     */
    private LinkappUser checkUser;

    /**
     * 测区测量数据
     */
    private Map<String, Map<Integer, List<ConcreteStrengthDetailDTO>>> areaMp;
}
