package com.easylinkin.linkappapi.concretestrength.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.api.ApiController;
import com.easylinkin.linkappapi.common.model.RequestModel;
import com.easylinkin.linkappapi.concretestrength.entity.ConcreteStrengthRecord;
import com.easylinkin.linkappapi.concretestrength.service.ConcreteStrengthRecordService;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import site.morn.rest.RestBuilders;
import site.morn.rest.RestMessage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * ConcreteStrengthRecord表控制层
 *
 * <AUTHOR>
 * @date 2022/10/23
 */
@Slf4j
@RestController
@RequestMapping("appConcreteStrengthRecord")
public class ConcreteStrengthRecordController extends ApiController {
    /**
     * 服务对象
     */
    @Resource
    private ConcreteStrengthRecordService appConcreteStrengthRecordService;

    /**
     * 分页查询所有数据
     *
     * @param requestModel 查询分页对象
     * @return 所有数据
     */
    @PostMapping("getPage")
    @ApiOperation("查询分页")
    public RestMessage selectPage(@RequestBody RequestModel<ConcreteStrengthRecord> requestModel) {
        Assert.notNull(requestModel.getCustomQueryParams(), "customQueryParams 不能为空");
        Assert.notNull(requestModel.getPage(), "page 不能为空");
        IPage<ConcreteStrengthRecord> record = appConcreteStrengthRecordService.selectPage(requestModel.getPage(), requestModel.getCustomQueryParams());
        return RestBuilders.successBuilder(record).build();
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("{id}")
    @ApiOperation("查询单条")
    public RestMessage selectOne(@PathVariable Serializable id) {
        return RestBuilders.successBuilder((this.appConcreteStrengthRecordService.getOneById(id))).build();
    }

    /**
     * 新增数据
     *
     * @param appConcreteStrengthRecord 实体对象
     * @return 新增结果
     */
    @PostMapping
    @ApiOperation("新增")
    public RestMessage insert(@RequestBody ConcreteStrengthRecord appConcreteStrengthRecord) {
        return RestBuilders.successBuilder().success((this.appConcreteStrengthRecordService.saveOne(appConcreteStrengthRecord))).build();
    }

    /**
     * 修改数据
     *
     * @param appConcreteStrengthRecord 实体对象
     * @return 修改结果
     */
    @PutMapping
    @ApiOperation("修改单条")
    public RestMessage update(@RequestBody ConcreteStrengthRecord appConcreteStrengthRecord) {
        return RestBuilders.successBuilder().success(this.appConcreteStrengthRecordService.updateOne(appConcreteStrengthRecord)).build();
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @DeleteMapping
    @ApiOperation("批量删除")
    public RestMessage delete(@RequestParam("idList") List<Long> idList) {
        return RestBuilders.successBuilder().success(this.appConcreteStrengthRecordService.deleteByIds(idList)).build();
    }

    @PostMapping("export")
    @ApiOperation("导出")
    public void export(@RequestBody ConcreteStrengthRecord appConcreteStrengthRecord, HttpServletRequest request, HttpServletResponse response) {
        appConcreteStrengthRecordService.export(appConcreteStrengthRecord, request, response);
    }
}

