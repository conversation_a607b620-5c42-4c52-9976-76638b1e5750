package com.easylinkin.linkappapi.penetsecuremanage.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/26 下午 12:04
 */
@Data
public class OrgaStructureStaffVO {
    /**
     * 人员id，对应花名册人员id
     */
    private String cpId;
    /**
     * 人员类型
     * 1管理人员，2安监人员，3工作人员
     */
    private String officePosition;
    /**
     * 人员照片
     */
    private String photo;
    /**
     * 岗位信息
     */
    private String post;
    /**
     * 姓名
     */
    private String realName;
    /**
     * 联系方式
     */
    private String linkPhone;


    private List<GridExpVO>  levelThree;



}
