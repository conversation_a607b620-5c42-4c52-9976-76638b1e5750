package com.easylinkin.linkappapi.penetsecuremanage.vo;

import com.easylinkin.linkappapi.common.translate.CodeI18n;
import com.easylinkin.linkappapi.penetsecuremanage.entity.RailLinkappGridManagementInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/20 下午 2:24
 */
@CodeI18n
@Data
public class RailLinkappGridManagementInfoVO extends RailLinkappGridManagementInfo {
    /**
     * 安监专务 联系方式
     */
    private String safetySupPhone;
    /**
     *安网格安全 联系方式
     */
    private String gridSecPhone;

    /**
     *
     施工员/领工员  联系方式
     */
    private String gridForemanPhone;

    /**
     *现场负责人/安全员 联系方式
     */

    private String gridSitemanPhone;
    /**
     * 紧急联系人联系方式
     */
    private String gridMeetPhone;
    /**
     * 安监专务头像
     */
    private String safetySupPict;
    /**
     * 安网格安全头像
     */
    private String gridSecPict;
    /**
     * 施工员/领工员头像
     */
    private String gridForemanPict;
    /**
     *现场负责人/安全员 头像
     */
    private String gridSitemanPict;
    /**
     *现紧急联系人头像
     */
    private String gridMeetPict;

}
