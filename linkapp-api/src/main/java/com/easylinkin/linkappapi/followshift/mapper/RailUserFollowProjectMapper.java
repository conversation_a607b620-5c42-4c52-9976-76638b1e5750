package com.easylinkin.linkappapi.followshift.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.easylinkin.linkappapi.followshift.entity.RailUserFollowProject;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户关注项目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface RailUserFollowProjectMapper extends BaseMapper<RailUserFollowProject> {

    /**
     * 根据用户ID查询关注的项目列表
     *
     * @param userId 用户ID
     * @return 关注的项目列表
     */
    List<RailUserFollowProject> getFollowProjectsByUserId(@Param("userId") Long userId);

    /**
     * 根据项目ID查询关注该项目的用户列表
     *
     * @param tenantId 项目ID
     * @return 关注该项目的用户列表
     */
    List<RailUserFollowProject> getFollowUsersByTenantId(@Param("tenantId") String tenantId);

    /**
     * 检查用户是否已关注指定项目
     *
     * @param userId 用户ID
     * @param tenantId 项目ID
     * @return 关注记录，如果未关注则返回null
     */
    RailUserFollowProject checkUserFollowProject(@Param("userId") Long userId, @Param("tenantId") String tenantId);
}
