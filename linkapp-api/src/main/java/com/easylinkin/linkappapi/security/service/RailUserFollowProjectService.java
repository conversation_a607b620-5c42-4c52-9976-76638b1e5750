package com.easylinkin.linkappapi.security.service;

import com.easylinkin.linkappapi.security.entity.RailUserFollowProject;

import java.util.List;

/**
 * <p>
 * 用户关注项目表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
public interface RailUserFollowProjectService {

    /**
     * 关注项目
     *
     * @param userId 用户ID
     * @param tenantId 项目ID
     * @return 是否成功
     */
    boolean followProject(Long userId, String tenantId);

    /**
     * 取消关注项目
     *
     * @param userId 用户ID
     * @param tenantId 项目ID
     * @return 是否成功
     */
    boolean unfollowProject(Long userId, String tenantId);

    /**
     * 检查用户是否已关注指定项目
     *
     * @param userId 用户ID
     * @param tenantId 项目ID
     * @return 是否已关注
     */
    boolean isFollowing(Long userId, String tenantId);

    /**
     * 根据用户ID查询关注的项目列表
     *
     * @param userId 用户ID
     * @return 关注的项目列表
     */
    List<RailUserFollowProject> getFollowProjectsByUserId(Long userId);

    /**
     * 根据项目ID查询关注该项目的用户列表
     *
     * @param tenantId 项目ID
     * @return 关注该项目的用户列表
     */
    List<RailUserFollowProject> getFollowUsersByTenantId(String tenantId);
}
