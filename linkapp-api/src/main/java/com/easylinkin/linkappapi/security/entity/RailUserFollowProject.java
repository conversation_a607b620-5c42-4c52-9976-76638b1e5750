package com.easylinkin.linkappapi.security.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.easylinkin.linkappapi.common.model.BaseEntity;
import com.easylinkin.linkappapi.common.translate.Code2Text;
import com.easylinkin.linkappapi.common.translate.CodeI18n;
import com.easylinkin.linkappapi.common.translate.UserNameTranslator;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户关注项目表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rail_user_follow_project")
@CodeI18n
public class RailUserFollowProject extends BaseEntity<RailUserFollowProject> {

    /**
     * 用户ID(linkapp_user)
     */
    @TableField("user_id_")
    @Code2Text(translateor = UserNameTranslator.class)
    private Long userId;

    /**
     * 项目ID(linkapp_tenant)
     */
    @TableField("tenant_id_")
    private String tenantId;

    /**
     * 逻辑删除标志
     */
    @TableField("is_deleted_")
    @TableLogic(value = "0", delval = "1")
    private Integer isDeleted;

    /**
     * 备注
     */
    @TableField("remark_")
    private String remark;
}
