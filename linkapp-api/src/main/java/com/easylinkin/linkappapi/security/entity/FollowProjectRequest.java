package com.easylinkin.linkappapi.security.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 关注项目请求参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@ApiModel(value = "关注项目请求参数", description = "关注项目请求参数")
public class FollowProjectRequest {

    /**
     * 项目ID(租户ID)
     */
    @ApiModelProperty(value = "项目ID(租户ID)", required = true)
    @NotBlank(message = "项目ID不能为空")
    private String tenantId;

    /**
     * 操作类型：true-关注，false-取消关注
     */
    @ApiModelProperty(value = "操作类型：true-关注，false-取消关注", required = true)
    @NotNull(message = "操作类型不能为空")
    private Boolean follow;
}
