package com.easylinkin.linkappapi.security.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.easylinkin.linkappapi.common.exception.BusinessException;
import com.easylinkin.linkappapi.common.exception.ExceptionEnum;
import com.easylinkin.linkappapi.security.entity.RailUserFollowProject;
import com.easylinkin.linkappapi.security.mapper.RailUserFollowProjectMapper;
import com.easylinkin.linkappapi.security.service.RailUserFollowProjectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户关注项目表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
public class RailUserFollowProjectServiceImpl implements RailUserFollowProjectService {

    @Resource
    private RailUserFollowProjectMapper railUserFollowProjectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean followProject(Long userId, String tenantId) {
        if (userId == null || StringUtils.isBlank(tenantId)) {
            throw new BusinessException(ExceptionEnum.CommonExceptionEnum.EXCEPTION_CODE_400, "用户ID和项目ID不能为空");
        }

        // 检查是否已经关注
        if (isFollowing(userId, tenantId)) {
            throw new BusinessException(ExceptionEnum.CommonExceptionEnum.EXCEPTION_CODE_400, "您已经关注了该项目");
        }

        // 创建关注记录
        RailUserFollowProject followProject = new RailUserFollowProject();
        followProject.setUserId(userId);
        followProject.setTenantId(tenantId);
        followProject.setCreatorId(userId);
        followProject.setCreateTime(new Date());
        followProject.setModifyId(userId);
        followProject.setModifyTime(new Date());

        int result = railUserFollowProjectMapper.insert(followProject);
        if (result > 0) {
            log.info("用户[{}]成功关注项目[{}]", userId, tenantId);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unfollowProject(Long userId, String tenantId) {
        if (userId == null || StringUtils.isBlank(tenantId)) {
            throw new BusinessException(ExceptionEnum.CommonExceptionEnum.EXCEPTION_CODE_400, "用户ID和项目ID不能为空");
        }

        // 查找关注记录
        RailUserFollowProject followProject = railUserFollowProjectMapper.checkUserFollowProject(userId, tenantId);
        if (followProject == null) {
            throw new BusinessException(ExceptionEnum.CommonExceptionEnum.EXCEPTION_CODE_400, "您还未关注该项目");
        }

        // 逻辑删除
        followProject.setIsDeleted(1);
        followProject.setModifyId(userId);
        followProject.setModifyTime(new Date());

        int result = railUserFollowProjectMapper.updateById(followProject);
        if (result > 0) {
            log.info("用户[{}]成功取消关注项目[{}]", userId, tenantId);
            return true;
        }
        return false;
    }

    @Override
    public boolean isFollowing(Long userId, String tenantId) {
        if (userId == null || StringUtils.isBlank(tenantId)) {
            return false;
        }

        RailUserFollowProject followProject = railUserFollowProjectMapper.checkUserFollowProject(userId, tenantId);
        return followProject != null;
    }

    @Override
    public List<RailUserFollowProject> getFollowProjectsByUserId(Long userId) {
        if (userId == null) {
            return CollUtil.newArrayList();
        }

        return railUserFollowProjectMapper.getFollowProjectsByUserId(userId);
    }

    @Override
    public List<RailUserFollowProject> getFollowUsersByTenantId(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return CollUtil.newArrayList();
        }

        return railUserFollowProjectMapper.getFollowUsersByTenantId(tenantId);
    }
}
