package com.easylinkin.linkappapi.security.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.easylinkin.linkappapi.security.entity.RailUserFollowProject;
import com.easylinkin.linkappapi.security.mapper.RailUserFollowProjectMapper;
import com.easylinkin.linkappapi.security.service.RailUserFollowProjectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户关注项目表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Slf4j
@Service
public class RailUserFollowProjectServiceImpl implements RailUserFollowProjectService {

    @Resource
    private RailUserFollowProjectMapper railUserFollowProjectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void followProject(Long userId, String tenantId) {
        // 检查是否已经关注
        if (isFollowing(userId, tenantId)) {
            return;
        }

        // 创建关注记录
        RailUserFollowProject followProject = new RailUserFollowProject();
        followProject.setUserId(userId);
        followProject.setTenantId(tenantId);
        followProject.setCreatorId(userId);
        followProject.setCreateTime(new Date());
        followProject.setModifyId(userId);
        followProject.setModifyTime(new Date());

        railUserFollowProjectMapper.insert(followProject);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unfollowProject(Long userId, String tenantId) {
        // 查找关注记录
        RailUserFollowProject followProject = railUserFollowProjectMapper.checkUserFollowProject(userId, tenantId);
        if (followProject == null) {
            return;
        }
        railUserFollowProjectMapper.deleteById(followProject.getId());
    }

    public boolean isFollowing(Long userId, String tenantId) {
        if (userId == null || StringUtils.isBlank(tenantId)) {
            return false;
        }

        RailUserFollowProject followProject = railUserFollowProjectMapper.checkUserFollowProject(userId, tenantId);
        return followProject != null;
    }

    @Override
    public List<RailUserFollowProject> getFollowProjectsByUserId(Long userId) {
        if (userId == null) {
            return CollUtil.newArrayList();
        }
        return railUserFollowProjectMapper.getFollowProjectsByUserId(userId);
    }
}
